import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '~/components/ui/pagination'

interface Props {
  currentPage: number
  handlePagePagination: (value: number) => void
  lastPage: number
}

function PagePagination({
  currentPage,
  handlePagePagination,
  lastPage,
}: Props) {
  const canGoPrevious = currentPage > 1
  const canGoNext = currentPage < lastPage

  return (
    <Pagination>
      <PaginationContent>
        {/* Previous Button */}
        <PaginationItem>
          <PaginationPrevious
            onClick={canGoPrevious ? () => handlePagePagination(currentPage - 1) : undefined}
            className={!canGoPrevious
              ? 'pointer-events-none opacity-50'
              : `cursor-pointer`}
          />
        </PaginationItem>

        {/* Page Numbers */}
        {Array.from(
          {
            length: lastPage,
          },
          (_, index) => {
          // Only show the first page, the last page, the current page, and two pages on either side of the current page
            if (
              index === 0
              || index === lastPage - 1
              || Math.abs(currentPage - (index + 1)) <= 2
            ) {
              return (
                <PaginationItem key={index}>
                  <PaginationLink
                    isActive={index + 1 === currentPage}
                    onClick={() => handlePagePagination(index + 1)}
                    className="cursor-pointer"
                  >
                    {index + 1}
                  </PaginationLink>
                </PaginationItem>
              )
            }
            else if (
            // Only show one ellipsis on either side of the current page
              index === currentPage - 4
              || index === currentPage + 2
            ) {
              return <PaginationEllipsis key={index} />
            }
            return null
          },
        )}

        {/* Next Button */}
        <PaginationItem>
          <PaginationNext
            onClick={canGoNext ? () => handlePagePagination(currentPage + 1) : undefined}
            className={!canGoNext
              ? 'pointer-events-none opacity-50'
              : `cursor-pointer`}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  )
}

export default PagePagination
