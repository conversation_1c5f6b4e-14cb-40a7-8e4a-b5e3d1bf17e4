import { graphql } from '~/gql'

export const GET_BAPTISMA_RECORD_LIST = graphql(`
  query GetBaptismaRecordList(
    $first: Int!
    $page: Int
    $baptisma_filter: BaptismaRecordFilterInput
  ) {
    getBaptismaRecordList(
      first: $first
      page: $page
      baptisma_filter: $baptisma_filter
    ) {
      paginator_info {
        last_page
      }
      data {
        id
        baptisma_registration_no: registration_no
        hming
        pa_hming
        nu_hming
        pian_ni
        baptisma_chan_ni
        khua
        chantirtu
        age
        register_book_number
        document {
          files {
            id
            doc_id
            path
          }
          id
          title
          body
          tags
          added_date
          is_classified
          category_id
        }
      }
    }
  }
`)
