import { graphql } from '~/gql'

export const GET_INNEIH_RECORD_LIST = graphql(`
  query GetInneihRecordList(
    $first: Int!
    $page: Int
    $inneih_filter: InneihRecordFilterInput
  ) {
    getInneihRecordList(
      first: $first
      page: $page
      inneih_filter: $inneih_filter
    ) {
      paginator_info {
        last_page
      }
      data {
        id
        inneih_registration_no: registration_no
        mipa_hming
        mipa_pa_hming
        mipa_khua
        hmeichhe_hming
        hmeichhe_pa_hming
        hmeichhe_khua
        inneih_ni
        hmun
        inneihtirtu
        register_book_number
        document {
          files {
            id
            doc_id
            path
          }
          id
          title
          body
          tags
          added_date
          is_classified
          category_id
        }
      }
    }
  }
`)
