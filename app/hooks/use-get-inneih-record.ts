import type { InneihRecordFilterInput } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { useState } from 'react'
import { GET_INNEIH_RECORD_LIST } from '~/graphql/queries/get-inneih-record-list'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetInneihRecord(enabled: boolean) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1).withOptions({ history: 'push' }))

  const [activeInneihFilter, setActiveInneihFilter] = useState<InneihRecordFilterInput | null>(null)

  const searchInneih = (data: InneihRecordFilterInput | null) => {
    setPage(1) // Reset to first page on new search
    setActiveInneihFilter(data)
  }

  const handlePage = (page: number) => {
    setPage(page)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-inneih-record', page, activeInneihFilter],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_INNEIH_RECORD_LIST,
        variables: {
          first: 20,
          page,
          inneih_filter: activeInneihFilter,
        },
      })
    },
    enabled,
  })

  const lastPage = data?.getInneihRecordList?.paginator_info?.last_page ?? 1

  return { data, isLoading, isError, page, handlePage, lastPage, searchInneih }
}
