import type { GetBaptismaRecordListQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import PagePagination from '~/components/common/page-pagination'
import DeleteIcon from '~/components/icons/delete-icon'
import UpdateIcon from '~/components/icons/update-icon'
import { Button, buttonVariants } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import { baseUrl } from '~/lib/base-url'
import { cn } from '~/lib/utils'
import UpdateBaptismaDialog from './update-baptisma-dialog'
import useDeleteDocument from './use-delete-document'
import useGetDocuments from './use-get-documents'

// type BaptismaRecordFromQuery = NonNullable<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]['extra_record']> & { __typename: 'BaptismaRecord' }
type BaptismaRecordFromQuery = NonNullable<GetBaptismaRecordListQuery['getBaptismaRecordList']['data']>[number]

export default function BaptismaFilters() {
  const { getBaptisma, handlePage, page, searchBaptisma, activeBaptismaFilter } = useGetDocuments()
  const [selectedRecord, setSelectedRecord] = useState<BaptismaRecordFromQuery>()
  // const [selectedDocument, setSelectedDocument] = useState<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]>()
  const [selectedDocument, setSelectedDocument] = useState<NonNullable<GetBaptismaRecordListQuery['getBaptismaRecordList']['data']>[number]['document']>()
  const [selectedId, setSelectedId] = useState('')
  const { deleteDocument } = useDeleteDocument()

  const { isOpen, toggle } = useBoolean()
  const { isOpen: openDelete, toggle: toggleDelete } = useBoolean()

  const handleDelete = () => {
    if (!selectedId) {
      return
    }
    deleteDocument.mutate(selectedId, {
      onSuccess: () => {
        setSelectedId('')
      },
    })
  }

  const form = useAppForm({
    defaultValues: {
      registration_no: '',
      hming: '',
      pa_hming: '',
      nu_hming: '',
      khua: '',
      pian_ni: '',
      baptisma_chan_ni: '',
      chantirtu: '',
    },
    onSubmit: async ({ value }) => {
      searchBaptisma(value)
    },
  })

  const data = getBaptisma.data?.getBaptismaRecordList?.data || []
  const lastPage = getBaptisma.data?.getBaptismaRecordList?.paginator_info?.last_page ?? 1

  // Use active filter values for download URL instead of current form values
  const activeChantirtu = activeBaptismaFilter?.chantirtu || ''
  const activeKhua = activeBaptismaFilter?.khua || ''

  return (
    <>
      <Card>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="grid grid-cols-4 gap-4"
          >
            <form.AppField
              name="registration_no"
              children={field => <field.InputField label="Registration No" />}
            />
            <form.AppField
              name="hming"
              children={field => <field.InputField label="Changtu hming" />}
            />
            <form.AppField
              name="nu_hming"
              children={field => <field.InputField label="Nu hming" />}
            />
            <form.AppField
              name="pa_hming"
              children={field => <field.InputField label="Pa hming" />}
            />

            <form.AppField
              name="khua"
              children={field => <field.InputField label="Khua" />}
            />
            <form.AppField
              name="pian_ni"
              children={field => <field.InputField label="Pian ni" type="date" />}
            />
            <form.AppField
              name="baptisma_chan_ni"
              children={field => <field.InputField label="Baptisma chan ni" type="date" />}
            />
            <form.AppField
              name="chantirtu"
              children={field => <field.InputField label="Chantirtu" />}
            />
            <div className="space-x-4">
              <Button type="submit" isLoading={getBaptisma.isLoading}>
                Search
              </Button>
              {activeChantirtu && data?.length > 0 && (
                <a
                  className={cn(buttonVariants({ variant: 'default' }))}
                  href={`${baseUrl}/export/baptisma-record?chantirtu=${activeChantirtu}${activeKhua ? `&khua=${activeKhua}` : ''}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Download
                </a>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
      {data?.length > 0 && (
        <div className="flex grow rounded-md bg-muted p-4">
          <div className="overflow-x-auto">
            <Table className="w-full min-w-[1000px] table-fixed">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-32">Reg no</TableHead>
                  <TableHead className="w-32">Reg book no.</TableHead>
                  <TableHead className="w-44">Baptisma changtu hming</TableHead>
                  <TableHead className="w-32">Baptisma chantirtu</TableHead>
                  <TableHead className="w-32">Age</TableHead>
                  <TableHead className="w-32">Pian ni</TableHead>
                  <TableHead className="w-32">Baptisma Chan ni</TableHead>
                  <TableHead className="w-32">Attached files</TableHead>
                  <TableHead className="w-32">Added on</TableHead>
                  <TableHead className="w-32">Classified</TableHead>
                  <TableHead className="w-32 text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.map(item => (
                  item && (
                    <TableRow key={item.id}>
                      <TableCell className="truncate">{item.baptisma_registration_no || '-'}</TableCell>
                      <TableCell className="truncate">{item.register_book_number || '-'}</TableCell>
                      <TableCell className="truncate">{item.hming || '-'}</TableCell>
                      <TableCell className="truncate">{item.chantirtu || '-'}</TableCell>
                      <TableCell className="truncate">{item.age || '-'}</TableCell>
                      <TableCell className="truncate">
                        <div className="flex items-center gap-2">
                          {item.pian_ni ? format(new Date(item.pian_ni), 'dd-MMM-yyyy') : '-'}
                        </div>
                      </TableCell>
                      <TableCell className="truncate">
                        <div className="flex items-center gap-2">
                          {item.baptisma_chan_ni ? format(new Date(item.baptisma_chan_ni), 'dd-MMM-yyyy') : '-'}
                        </div>
                      </TableCell>
                      <TableCell className="truncate">{item.document.files?.length || '0'}</TableCell>
                      <TableCell className="truncate">{item.document.added_date ? format(new Date(item.document.added_date), 'dd-MMM-yyyy') : '-' }</TableCell>
                      <TableCell className="truncate">{item.document.is_classified ? 'Yes' : 'No'}</TableCell>
                      <TableCell>
                        <div className="flex justify-end gap-x-2">
                          <AppTooltip message="Update">
                            <Button
                              onClick={() => {
                                if (item) {
                                  setSelectedRecord(item)
                                  setSelectedDocument(item.document)
                                }
                                toggle(true)
                              }}
                              size="icon"
                              variant="success"
                            >
                              <UpdateIcon />
                            </Button>
                          </AppTooltip>
                          <AppTooltip message="Delete document">
                            <Button
                              onClick={() => {
                                if (item.id) {
                                  setSelectedId(item.id)
                                  toggleDelete(true)
                                }
                              }}
                              size="icon"
                              variant="destructive"
                            >
                              <DeleteIcon />
                            </Button>
                          </AppTooltip>
                        </div>
                      </TableCell>
                    </TableRow>
                  )))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
      {lastPage > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePage}
          lastPage={lastPage}
        />
      )}
      {selectedRecord && selectedDocument && (
        <UpdateBaptismaDialog
          isOpen={isOpen}
          toggle={(open) => {
            toggle(open)
            setSelectedRecord(undefined)
            setSelectedDocument(undefined)
          }}
          record={selectedRecord}
          _document={selectedDocument}
        />
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deleteDocument.isPending}
          handleConfirm={handleDelete}
          open={openDelete}
          handleOpenChange={toggleDelete}
        />
      )}
    </>
  )
}
