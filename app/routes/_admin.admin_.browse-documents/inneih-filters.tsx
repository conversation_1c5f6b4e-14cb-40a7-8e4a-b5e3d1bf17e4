import type { GetInneihRecordListQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import PagePagination from '~/components/common/page-pagination'
import DeleteIcon from '~/components/icons/delete-icon'
import UpdateIcon from '~/components/icons/update-icon'
import { Button, buttonVariants } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import { baseUrl } from '~/lib/base-url'
import { cn } from '~/lib/utils'
import UpdateInneihDialog from './update-inneih-dialog'
import useDeleteDocument from './use-delete-document'
import useGetDocuments from './use-get-documents'

// type InneihRecordFromQuery = NonNullable<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]['extra_record']> & { __typename: 'InneihRecord' }
type InneihRecordFromQuery = NonNullable<GetInneihRecordListQuery['getInneihRecordList']['data']>[number]

export default function InneihFilters() {
  const { getInneih, handlePage, page, searchInneih, activeInneihFilter } = useGetDocuments()
  const [selectedRecord, setSelectedRecord] = useState<InneihRecordFromQuery>()
  const [selectedDocument, setSelectedDocument] = useState<NonNullable<GetInneihRecordListQuery['getInneihRecordList']['data']>[number]['document']>()
  const [selectedId, setSelectedId] = useState('')
  const { deleteDocument } = useDeleteDocument()

  const { isOpen, toggle } = useBoolean()
  const { isOpen: openDelete, toggle: toggleDelete } = useBoolean()

  const handleDelete = () => {
    if (!selectedId) {
      return
    }
    deleteDocument.mutate(selectedId, {
      onSuccess: () => {
        setSelectedId('')
      },
    })
  }

  const form = useAppForm({
    defaultValues: {
      registration_no: '',
      mipa_hming: '',
      mipa_pa_hming: '',
      hmeichhe_hming: '',
      hmeichhe_pa_hming: '',
      inneih_ni: '',
      hmun: '',
      inneihtirtu: '',
    },
    onSubmit: async ({ value }) => {
      searchInneih(value)
    },
  })

  const data = getInneih.data?.getInneihRecordList?.data || []
  const lastPage = getInneih.data?.getInneihRecordList?.paginator_info?.last_page ?? 1

  // Use active filter values for download URL instead of current form values
  const activeInneihtirtu = activeInneihFilter?.inneihtirtu || ''
  const activeHmun = activeInneihFilter?.hmun || ''

  return (
    <>
      <Card>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="grid grid-cols-4 gap-4"
          >
            <form.AppField
              name="registration_no"
              children={field => <field.InputField label="Registration No" />}
            />
            <form.AppField
              name="mipa_hming"
              children={field => <field.InputField label="Mipa hming" />}
            />
            <form.AppField
              name="mipa_pa_hming"
              children={field => <field.InputField label="Mipa Pa hming" />}
            />
            <form.AppField
              name="hmeichhe_hming"
              children={field => <field.InputField label="Hmeichhe hming" />}
            />

            <form.AppField
              name="hmeichhe_pa_hming"
              children={field => <field.InputField label="Hmeichhe nu hming" />}
            />
            <form.AppField
              name="inneih_ni"
              children={field => <field.InputField label="Inneih ni" type="date" />}
            />
            <form.AppField
              name="hmun"
              children={field => <field.InputField label="Hmun" />}
            />
            <form.AppField
              name="inneihtirtu"
              children={field => <field.InputField label="Inneih tir tu" />}
            />
            <div className="space-x-4">
              <Button type="submit" isLoading={getInneih.isLoading}>
                Search
              </Button>
              {activeInneihtirtu && data?.length > 0 && (
                <a
                  className={cn(buttonVariants({ variant: 'default' }))}
                  href={`${baseUrl}/export/inneih-record?inneihtirtu=${activeInneihtirtu}${activeHmun ? `&hmun=${activeHmun}` : ''}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Download
                </a>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
      {data?.length > 0 && (
        <div className="flex grow rounded-md bg-muted p-4">
          <Table className="w-full min-w-[1000px] table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead className="w-32">Reg no</TableHead>
                <TableHead className="w-32">Reg book no.</TableHead>
                <TableHead className="w-32">Mipa hming</TableHead>
                <TableHead className="w-32">Mipa pa hming</TableHead>
                <TableHead className="w-32">Mipa Khua</TableHead>
                <TableHead className="w-32">Hmeichhe hming</TableHead>
                <TableHead className="w-32">Hmeichhe pa hming</TableHead>
                <TableHead className="w-32">Hmeichhe Khua</TableHead>
                <TableHead className="w-32">Inneih ni</TableHead>
                <TableHead className="w-32">Hmun</TableHead>
                <TableHead className="w-32">Inneihtirtu</TableHead>
                <TableHead className="w-20 text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.map(item => (
                item && (
                  <TableRow key={item.id}>
                    <TableCell className="truncate">{item.inneih_registration_no || '-'}</TableCell>
                    <TableCell className="truncate">{item.register_book_number || '-'}</TableCell>
                    <TableCell className="truncate">{item.mipa_hming || '-'}</TableCell>
                    <TableCell className="truncate">{item.mipa_pa_hming || '-'}</TableCell>
                    <TableCell className="truncate">{item.mipa_khua || '-'}</TableCell>
                    <TableCell className="truncate">{item.hmeichhe_hming || '-'}</TableCell>
                    <TableCell className="truncate">{item.hmeichhe_pa_hming || '-'}</TableCell>
                    <TableCell className="truncate">{item.hmeichhe_khua || '-'}</TableCell>
                    <TableCell className="truncate">{item.inneih_ni ? format(new Date(item.inneih_ni), 'dd-MMM-yyyy') : '-'}</TableCell>
                    <TableCell className="truncate">{item.hmun || '-'}</TableCell>
                    <TableCell className="truncate">{item.inneihtirtu || '-'}</TableCell>
                    <TableCell>
                      <div className="flex justify-end gap-x-2">
                        <AppTooltip message="Update">
                          <Button
                            onClick={() => {
                              if (item) {
                                setSelectedRecord(item)
                                setSelectedDocument(item.document)
                              }
                              toggle(true)
                            }}
                            size="icon"
                            variant="success"
                          >
                            <UpdateIcon />
                          </Button>
                        </AppTooltip>
                        <AppTooltip message="Delete document">
                          <Button
                            onClick={() => {
                              if (item.id) {
                                setSelectedId(item.id)
                                toggleDelete(true)
                              }
                            }}
                            size="icon"
                            variant="destructive"
                          >
                            <DeleteIcon />
                          </Button>
                        </AppTooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                )))}
            </TableBody>
          </Table>

        </div>
      )}
      {lastPage > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePage}
          lastPage={lastPage}
        />
      )}
      {selectedRecord && selectedDocument && (
        <UpdateInneihDialog
          isOpen={isOpen}
          toggle={(open) => {
            toggle(open)
            setSelectedRecord(undefined)
            setSelectedDocument(undefined)
          }}
          record={selectedRecord}
          _document={selectedDocument}
        />
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deleteDocument.isPending}
          handleConfirm={handleDelete}
          open={openDelete}
          handleOpenChange={toggleDelete}
        />
      )}
    </>
  )
}
