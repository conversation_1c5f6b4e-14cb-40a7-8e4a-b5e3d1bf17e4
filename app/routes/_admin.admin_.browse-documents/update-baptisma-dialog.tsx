import type { UpdateRecordType } from './schema'
import type { GetBaptismaRecordListQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { DownloadIcon } from 'lucide-react'
import { useRef, useState } from 'react'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { useAppForm } from '~/hooks/form'
import useGenerateDownloadLink from '~/hooks/use-generate-download-link'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import DeleteFilePopover from './delete-file-popover'
import { updateRecordSchema } from './schema'
import useUpdateDocuments from './use-update-documents'

type BaptismaRecordFromQuery = NonNullable<GetBaptismaRecordListQuery['getBaptismaRecordList']['data']>[number]
type DocumentFromQuery = NonNullable<GetBaptismaRecordListQuery['getBaptismaRecordList']['data']>[number]['document']

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  record: BaptismaRecordFromQuery
  _document: DocumentFromQuery
}

export default function UpdateBaptismaDialog({ isOpen, toggle, record, _document }: Props) {
  const [files, setFiles] = useState(_document.files || [])
  const { updateBaptismaRecord } = useUpdateDocuments()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { generateDownloadLink } = useGenerateDownloadLink()

  const generateLink = (_fileId: string) => {
    generateDownloadLink.mutate(_fileId, {
      onSuccess: (data) => {
        window.open(data.generateDownloadLink, '_blank')
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  const form = useAppForm({
    defaultValues: {
      baptisma_record: {
        id: record.id,
        registration_no: record.baptisma_registration_no || '',
        hming: record.hming || '',
        pa_hming: record.pa_hming || '',
        nu_hming: record.nu_hming || '',
        khua: record.khua || '',
        pian_ni: record.pian_ni ? format(new Date(record.pian_ni), 'yyyy-MM-dd') : '',
        baptisma_chan_ni: record.baptisma_chan_ni ? format(new Date(record.baptisma_chan_ni), 'yyyy-MM-dd') : '',
        chantirtu: record.chantirtu || '',
        register_book_number: record.register_book_number || '',
        age: record.age || '',
      },
      document: {
        id: _document.id,
        title: _document.title || '',
        body: _document.body || '',
        tags: _document.tags || '',
        is_classified: _document.is_classified || false,
        category_id: _document.category_id || '',
        added_date: _document.added_date ? format(new Date(_document.added_date), 'yyyy-MM-dd') : '',
        files: undefined as File[] | undefined,
      },
    } as UpdateRecordType,
    validators: {
      onSubmit: updateRecordSchema,
    },
    onSubmit: async ({ value }) => {
      await updateBaptismaRecord.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
          // Manually clear the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = ''
          }
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent className="w-full min-w-384">
        <DialogHeader>
          <DialogTitle>
            Update Baptisma Record
          </DialogTitle>
          <DialogDescription>
            Enter update details
          </DialogDescription>
        </DialogHeader>
        <div className="flex gap-4">
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="flex shrink-0 basis-2/3 flex-col gap-4"
          >
            <div className="flex gap-x-8">
              <div className="w-full">
                <form.AppField
                  name="baptisma_record.registration_no"
                  children={field => <field.InputField label="Registration No" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="baptisma_record.hming"
                  children={field => <field.InputField label="Hming" />}
                />
              </div>
            </div>
            <div className="flex gap-x-8">
              <div className="w-full">
                <form.AppField
                  name="baptisma_record.pa_hming"
                  children={field => <field.InputField label="Pa hming" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="baptisma_record.nu_hming"
                  children={field => <field.InputField label="Nu hming" />}
                />
              </div>
            </div>
            <div className="flex gap-x-8">
              <div className="w-full">
                <form.AppField
                  name="baptisma_record.pian_ni"
                  children={field => <field.InputField label="Pian ni" type="date" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="baptisma_record.baptisma_chan_ni"
                  children={field => <field.InputField label="Baptisma Chan ni" type="date" />}
                />
              </div>
            </div>
            <div className="flex gap-x-8">
              <div className="w-full">
                <form.AppField
                  name="baptisma_record.khua"
                  children={field => <field.InputField label="Khua" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="baptisma_record.chantirtu"
                  children={field => <field.InputField label="Baptisma chantirtu" />}
                />
              </div>
            </div>
            <div className="flex gap-x-8">
              <div className="basis-1/2">
                <form.AppField
                  name="baptisma_record.age"
                  children={field => <field.InputField label="Age" />}
                />
              </div>
              <div className="basis-1/2">
                <form.AppField
                  name="baptisma_record.register_book_number"
                  children={field => <field.InputField label="Register book number" />}
                />
              </div>
            </div>
            <h3 className="text-lg font-semibold">Document Information</h3>
            <div className="flex gap-x-4">
              <div className="w-full">
                <form.AppField
                  name="document.title"
                  children={field => <field.InputField label="Title" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="document.tags"
                  children={field => <field.PillInput label="Tags" />}
                />
              </div>
            </div>
            <div className="w-full">
              <form.AppField
                name="document.body"
                children={field => <field.InputRichText label="Body" />}
              />
            </div>
            <div className="flex gap-x-4">
              <div className="w-full">
                <form.Field
                  name="document.files"
                  children={field => (
                    <Label className="flex flex-col items-start gap-y-2">
                      <div>Upload file</div>
                      <Input
                        ref={fileInputRef}
                        type="file"
                        multiple={true}
                        accept="image/*,application/pdf"
                        onChange={(e) => {
                          if (e.target.files && e.target.files.length > 0) {
                            field.handleChange(Array.from(e.target.files))
                          }
                          else {
                            field.handleChange([])
                          }
                        }}
                        className="bg-white"
                      />

                    </Label>
                  )}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="document.added_date"
                  children={field => <field.InputField type="date" label="Added date" />}
                />
              </div>
            </div>
            <div>
              <form.AppField
                name="document.is_classified"
                children={field => <field.CheckboxField label="Classified" />}
              />
            </div>
            <div className="flex justify-end">
              <form.AppForm>
                <form.SubmitButton label="Update" />
              </form.AppForm>

            </div>

          </form>
          <div className="flex basis-1/3 flex-col border-l-2 pl-4">
            <h3 className="mb-4 text-lg font-semibold">File list</h3>
            <ul className="space-y-4">
              {files?.map(file => (
                <li key={file.id} className="flex justify-between gap-x-4">
                  <div className="flex-1 text-sm break-all" title={file.path}>
                    {file.path}
                  </div>
                  <div className="flex gap-x-4">
                    <Button
                      size="icon"
                      type="button"
                      onClick={() => generateLink(file.id)}
                    >
                      <DownloadIcon className="size-6 shrink-0" />
                    </Button>
                    <DeleteFilePopover
                      onRemove={() => {
                        setFiles(files?.filter(f => f.id !== file.id))
                      }}
                      id={file.id}
                    />
                  </div>
                </li>
              ))}

            </ul>
          </div>

        </div>
      </DialogContent>
    </Dialog>
  )
}
