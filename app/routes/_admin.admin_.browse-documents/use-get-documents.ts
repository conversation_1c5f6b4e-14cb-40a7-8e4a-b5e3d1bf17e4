import type { BaptismaRecordFilterInput, InneihRecordFilterInput, OtherRecordInput } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { parseAsInteger, useQueryState } from 'nuqs'
import { useState } from 'react'
import { GET_BAPTISMA_RECORD_LIST } from '~/graphql/queries/get-baptisma-record-list'
import { GET_INNEIH_RECORD_LIST } from '~/graphql/queries/get-inneih-record-list'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_DOCUMENTS } from './graphql'

export default function useGetDocuments() {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1).withOptions({ history: 'push' }))

  // State to store active search filters - only set when search is triggered
  const [activeBaptismaFilter, setActiveBaptismaFilter] = useState<BaptismaRecordFilterInput | null>(null)
  const [activeInneihFilter, setActiveInneihFilter] = useState<InneihRecordFilterInput | null>(null)
  const [activeOthersFilter, setActiveOthersFilter] = useState<OtherRecordInput | null>(null)

  const handlePage = (page: number) => {
    setPage(page)
  }

  // Search trigger functions - these activate the queries
  const searchBaptisma = (data: BaptismaRecordFilterInput) => {
    setPage(1) // Reset to first page on new search
    setActiveBaptismaFilter(data)
    // Clear other active filters
    setActiveInneihFilter(null)
    setActiveOthersFilter(null)
  }

  const searchInneih = (data: InneihRecordFilterInput) => {
    setPage(1) // Reset to first page on new search
    setActiveInneihFilter(data)
    // Clear other active filters
    setActiveBaptismaFilter(null)
    setActiveOthersFilter(null)
  }

  const searchOthers = (data: OtherRecordInput) => {
    setPage(1) // Reset to first page on new search
    setActiveOthersFilter(data)
    // Clear other active filters
    setActiveBaptismaFilter(null)
    setActiveInneihFilter(null)
  }

  // Baptisma query - only runs when activeBaptismaFilter is set
  const getBaptisma = useQuery({
    queryKey: ['get-baptisma', page, activeBaptismaFilter],
    queryFn: async () => {
      if (!activeBaptismaFilter)
        return null

      const client = await graphqlClient()
      return client.request({
        document: GET_BAPTISMA_RECORD_LIST,
        variables: {
          first: 20,
          page,
          baptisma_filter: {
            chantirtu: activeBaptismaFilter.chantirtu || undefined,
            hming: activeBaptismaFilter.hming || undefined,
            pa_hming: activeBaptismaFilter.pa_hming || undefined,
            nu_hming: activeBaptismaFilter.nu_hming || undefined,
            khua: activeBaptismaFilter.khua || undefined,
            registration_no: activeBaptismaFilter.registration_no || undefined,
            pian_ni: activeBaptismaFilter.pian_ni ? format(new Date(activeBaptismaFilter.pian_ni), 'yyyy-MM-dd') : undefined,
            baptisma_chan_ni: activeBaptismaFilter.baptisma_chan_ni ? format(new Date(activeBaptismaFilter.baptisma_chan_ni), 'yyyy-MM-dd') : undefined,
          },
        },
      })
    },
    enabled: !!activeBaptismaFilter, // Only run when filter is active
  })

  // Inneih query - only runs when activeInneihFilter is set
  const getInneih = useQuery({
    queryKey: ['get-inneih', page, activeInneihFilter],
    queryFn: async () => {
      if (!activeInneihFilter)
        return null

      const client = await graphqlClient()
      return client.request({
        document: GET_INNEIH_RECORD_LIST,
        variables: {
          first: 20,
          page,
          inneih_filter: {
            inneihtirtu: activeInneihFilter.inneihtirtu || undefined,
            registration_no: activeInneihFilter.registration_no || undefined,
            hmun: activeInneihFilter.hmun || undefined,
            mipa_hming: activeInneihFilter.mipa_hming || undefined,
            mipa_pa_hming: activeInneihFilter.mipa_pa_hming || undefined,
            hmeichhe_hming: activeInneihFilter.hmeichhe_hming || undefined,
            hmeichhe_pa_hming: activeInneihFilter.hmeichhe_pa_hming || undefined,
            inneih_ni: activeInneihFilter.inneih_ni ? format(new Date(activeInneihFilter.inneih_ni), 'yyyy-MM-dd') : undefined,
          },
        },
      })
    },
    enabled: !!activeInneihFilter, // Only run when filter is active
  })

  // Others query - only runs when activeOthersFilter is set
  const getOthers = useQuery({
    queryKey: ['get-others', page, activeOthersFilter],
    queryFn: async () => {
      if (!activeOthersFilter)
        return null

      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENTS,
        variables: {
          first: 20,
          page,
          others_filter: {
            ...activeOthersFilter,
            added_date: activeOthersFilter.added_date ? format(new Date(activeOthersFilter.added_date), 'yyyy-MM-dd') : undefined,
          },
        },
      })
    },
    enabled: !!activeOthersFilter, // Only run when filter is active
  })

  return {
    getBaptisma,
    getInneih,
    getOthers,
    searchBaptisma,
    searchInneih,
    searchOthers,
    page,
    handlePage,
    // Helper to check which search is currently active
    activeSearchType: activeBaptismaFilter ? 'baptisma' : activeInneihFilter ? 'inneih' : activeOthersFilter ? 'others' : null,
    // Active filter values for generating download URLs
    activeInneihFilter,
    activeBaptismaFilter,
    activeOthersFilter,
  }
}
