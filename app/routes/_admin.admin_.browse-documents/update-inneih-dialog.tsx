import type { UpdateRecordType } from './schema'
import type { GetInneihRecordListQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { DownloadIcon } from 'lucide-react'
import { useRef, useState } from 'react'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { useAppForm } from '~/hooks/form'
import useGenerateDownloadLink from '~/hooks/use-generate-download-link'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import DeleteFilePopover from './delete-file-popover'
import useUpdateDocuments from './use-update-documents'

type InneihRecordFromQuery = NonNullable<GetInneihRecordListQuery['getInneihRecordList']['data']>[number]
type DocumentFromQuery = NonNullable<GetInneihRecordListQuery['getInneihRecordList']['data']>[number]['document']

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  record: InneihRecordFromQuery
  _document: DocumentFromQuery
}

export default function UpdateInneihDialog({ isOpen, toggle, record, _document }: Props) {
  const { updateInneihRecord } = useUpdateDocuments()
  const [files, setFiles] = useState(_document.files || [])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { generateDownloadLink } = useGenerateDownloadLink()

  const generateLink = (_fileId: string) => {
    generateDownloadLink.mutate(_fileId, {
      onSuccess: (data) => {
        window.open(data.generateDownloadLink, '_blank')
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  const form = useAppForm({
    defaultValues: {
      inneih_record: {
        id: record.id,
        registration_no: record.inneih_registration_no || '',
        mipa_hming: record.mipa_hming || '',
        mipa_pa_hming: record.mipa_pa_hming || '',
        mipa_khua: record.mipa_khua || '',
        hmeichhe_hming: record.hmeichhe_hming || '',
        hmeichhe_pa_hming: record.hmeichhe_pa_hming || '',
        hmeichhe_khua: record.hmeichhe_khua || '',
        hmun: record.hmun || '',
        inneih_ni: record.inneih_ni ? format(new Date(record.inneih_ni), 'yyyy-MM-dd') : '',
        inneihtirtu: record.inneihtirtu || '',
        register_book_number: record.register_book_number || '',
      },
      document: {
        id: _document.id,
        title: _document.title || '',
        body: _document.body || '',
        tags: _document.tags || '',
        is_classified: _document.is_classified || false,
        category_id: _document.category_id || '',
        added_date: _document.added_date ? format(new Date(_document.added_date), 'yyyy-MM-dd') : '',
        files: undefined as File[] | undefined,
      },
    } as UpdateRecordType,
    onSubmit: async ({ value }) => {
      await updateInneihRecord.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
          // Manually clear the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = ''
          }
          form.reset()
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent className="w-full min-w-384">
        <DialogHeader>
          <DialogTitle>
            Update Inneih Record
          </DialogTitle>
          <DialogDescription>
            Enter update details
          </DialogDescription>
        </DialogHeader>
        <div className="flex gap-x-8">
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="flex basis-2/3 flex-col gap-4"
          >
            <div className="flex gap-x-8">
              <div className="basis-1/2">
                <form.AppField
                  name="inneih_record.registration_no"
                  children={field => <field.InputField label="Registration No" />}
                />
              </div>
              <div className="basis-1/2" />
            </div>
            <div className="flex gap-x-8">
              <div className="w-full">
                <form.AppField
                  name="inneih_record.mipa_hming"
                  children={field => <field.InputField label="Mipa hming" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="inneih_record.mipa_pa_hming"
                  children={field => <field.InputField label="Mipa pa hming" />}
                />
              </div>
            </div>
            <div className="flex gap-x-8">
              <div className="w-full">
                <form.AppField
                  name="inneih_record.mipa_khua"
                  children={field => <field.InputField label="Mipa khua" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="inneih_record.hmeichhe_hming"
                  children={field => <field.InputField label="Hmeichhe hming" />}
                />
              </div>
            </div>
            <div className="flex gap-x-8">
              <div className="w-full">
                <form.AppField
                  name="inneih_record.hmeichhe_pa_hming"
                  children={field => <field.InputField label="Hmeichhe pa hming" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="inneih_record.hmeichhe_khua"
                  children={field => <field.InputField label="Hmeichhe khua" />}
                />
              </div>
            </div>
            <div className="flex gap-x-8">
              <div className="w-full">
                <form.AppField
                  name="inneih_record.inneih_ni"
                  children={field => <field.InputField label="Inneih ni" type="date" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="inneih_record.hmun"
                  children={field => <field.InputField label="Hmun" />}
                />
              </div>
            </div>
            <div className="flex gap-x-8">
              <div className="basis-1/2">
                <form.AppField
                  name="inneih_record.inneihtirtu"
                  children={field => <field.InputField label="Inneihtirtu" />}
                />
              </div>
              <div className="basis-1/2">
                <form.AppField
                  name="inneih_record.register_book_number"
                  children={field => <field.InputField label="Register book number" />}
                />
              </div>
            </div>
            <h3 className="text-lg font-semibold">Document Information</h3>
            <div className="flex gap-x-4">
              <div className="w-full">
                <form.AppField
                  name="document.title"
                  children={field => <field.InputField label="Title" />}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="document.tags"
                  children={field => <field.PillInput label="Tags" />}
                />
              </div>
            </div>
            <div className="w-full">
              <form.AppField
                name="document.body"
                children={field => <field.InputRichText label="Body" />}
              />
            </div>
            <div className="flex gap-x-4">
              <div className="w-full">
                <form.Field
                  name="document.files"
                  children={field => (
                    <Label className="flex flex-col items-start gap-y-2">
                      <div>Upload file</div>
                      <Input
                        ref={fileInputRef}
                        type="file"
                        multiple={true}
                        accept="image/*,application/pdf"
                        onChange={(e) => {
                          if (e.target.files && e.target.files.length > 0) {
                            field.handleChange(Array.from(e.target.files))
                          }
                          else {
                            field.handleChange([])
                          }
                        }}
                        className="bg-white"
                      />

                    </Label>
                  )}
                />
              </div>
              <div className="w-full">
                <form.AppField
                  name="document.added_date"
                  children={field => <field.InputField type="date" label="Added date" />}
                />
              </div>
            </div>
            <div className="flex gap-x-4">
              <div className="w-full">
                <form.AppField
                  name="document.category_id"
                  children={field => <field.InputField label="Category ID" />}
                />
              </div>
            </div>
            <div>
              <form.AppField
                name="document.is_classified"
                children={field => <field.CheckboxField label="Classified" />}
              />
            </div>
            <DialogFooter>
              <form.AppForm>
                <form.SubmitButton label="Update" />
              </form.AppForm>
            </DialogFooter>
          </form>
          <div className="flex basis-1/3 flex-col border-l-2 pl-4">
            <h3 className="mb-4 text-lg font-semibold">File list</h3>
            <ul className="space-y-4">
              {files?.map(file => (
                <li key={file.id} className="flex justify-between gap-x-4">
                  <div className="flex-1 text-sm break-all" title={file.path}>
                    {file.path}
                  </div>
                  <div className="flex gap-x-4">
                    <Button
                      size="icon"
                      type="button"
                      onClick={() => generateLink(file.id)}
                    >
                      <DownloadIcon className="size-6 shrink-0" />
                    </Button>
                    <DeleteFilePopover
                      onRemove={() => {
                        setFiles(files?.filter(f => f.id !== file.id))
                      }}
                      id={file.id}
                    />
                  </div>
                </li>
              ))}

            </ul>
          </div>

        </div>
      </DialogContent>
    </Dialog>
  )
}
