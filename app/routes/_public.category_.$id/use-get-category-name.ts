import { useQuery } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_CATEGORY_NAME } from './graphql'

export default function useGetCategoryName(id: string) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-category-name', id],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_CATEGORY_NAME,
        variables: {
          id,
        },
      })
    },
    enabled: !!id,
  })

  const categoryName = data?.getCategoryById?.name
  const parentName = data?.getCategoryById?.parent?.id !== '1' ? data?.getCategoryById?.parent?.name : null
  const parentId = data?.getCategoryById?.parent?.id
  const grandParentName = data?.getCategoryById?.parent?.parent?.id !== '1' ? data?.getCategoryById?.parent?.parent?.name : null
  const grandParentId = data?.getCategoryById?.parent?.parent?.id

  return { categoryName, isLoading, isError, parentName, grandParentName, parentId, grandParentId }
}
