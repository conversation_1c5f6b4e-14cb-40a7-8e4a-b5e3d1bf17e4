import type { OtherRecordInput } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { useState } from 'react'
import { BAPTISMA_CATEGORY_ID, INNEIH_CATEGORY_ID } from '~/lib/constants'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_DOCUMENTS_BY_CATEGORY_ID } from './graphql'

interface Props {
  id?: string
}

export default function useGetDocumentsByCategoryId({ id }: Props) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1).withOptions({ history: 'push' }))

  const [activeOthersFilter, setActiveOthersFilter] = useState<OtherRecordInput | null>(null)

  const handlePage = (page: number) => {
    setPage(page)
  }

  const searchOthers = (data: OtherRecordInput | null) => {
    setPage(1) // Reset to first page on new search
    setActiveOthersFilter(data)
    // Clear other active filters
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-documents-by-category-id', id, page],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENTS_BY_CATEGORY_ID,
        variables: {
          first: 20,
          page,
          category_id: id!,
          others_filter: activeOthersFilter,
        },
      })
    },
    enabled: !!id && id !== BAPTISMA_CATEGORY_ID && id !== INNEIH_CATEGORY_ID,
  })

  const lastPage = data?.getDocumentsByCategoryId?.paginator_info?.last_page || 1

  return { data, isLoading, isError, page, handlePage, lastPage, searchOthers }
}
