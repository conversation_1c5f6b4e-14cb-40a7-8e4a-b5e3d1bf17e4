import type { GetBaptismaRecordListQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { useNavigate } from 'react-router'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { BAPTISMA_CATEGORY_ID } from '~/lib/constants'

type Document = NonNullable<GetBaptismaRecordListQuery['getBaptismaRecordList']['data']>

interface Props {
  documents: Document
}

// Component to render a single baptisma record row
function BaptismaRecordRow({
  baptismaRecord,
  documentId,
}: {
  documentId: string
  baptismaRecord: NonNullable<GetBaptismaRecordListQuery['getBaptismaRecordList']['data']>[number]
}) {
  const record = baptismaRecord
  const navigate = useNavigate()

  return (
    <TableRow
      role="link"
      onClick={() => {
        navigate(`/category/${BAPTISMA_CATEGORY_ID}/view/${documentId}`)
      }}
      className="hover:bg-muted"
    >
      <TableCell>{record.baptisma_registration_no || '-'}</TableCell>
      <TableCell>{record.hming || '-'}</TableCell>
      <TableCell>{record.pa_hming || '-'}</TableCell>
      <TableCell>{record.nu_hming || '-'}</TableCell>
      <TableCell>{record.khua || '-'}</TableCell>
      <TableCell>
        {record.pian_ni ? format(new Date(record.pian_ni), 'dd-MMM-yyyy') : '-'}
      </TableCell>
      <TableCell>
        {record.baptisma_chan_ni ? format(new Date(record.baptisma_chan_ni), 'dd-MMM-yyyy') : '-'}
      </TableCell>
      <TableCell>{record.chantirtu || '-'}</TableCell>
    </TableRow>
  )
}

export default function BaptismaTable({ documents }: Props) {
  return (
    <div className="my-4 flex grow rounded-md bg-white p-4">
      <Table className="w-full min-w-[1000px] table-fixed">
        <TableHeader>
          <TableRow>
            <TableHead className="w-32">Reg no</TableHead>
            <TableHead className="w-32">Hming</TableHead>
            <TableHead className="w-32">Pa Hming</TableHead>
            <TableHead className="w-32">Nu Hming</TableHead>
            <TableHead className="w-32">Khua</TableHead>
            <TableHead className="w-32">Pian ni</TableHead>
            <TableHead className="w-32">Baptisma chan ni</TableHead>
            <TableHead className="w-32">Chantirtu</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents && documents.length > 0
            ? (
                documents.map(document => (
                  <BaptismaRecordRow
                    documentId={document.document.id}
                    key={document.id}
                    baptismaRecord={document}
                  />
                ))
              )
            : (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center text-muted-foreground"
                  >
                    No baptisma records found
                  </TableCell>
                </TableRow>
              )}
        </TableBody>
      </Table>
    </div>
  )
}
