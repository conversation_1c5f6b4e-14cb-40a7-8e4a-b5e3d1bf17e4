import type { GetInneihRecordListQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { useNavigate } from 'react-router'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { INNEIH_CATEGORY_ID } from '~/lib/constants'

type Document = NonNullable<GetInneihRecordListQuery['getInneihRecordList']['data']>

interface Props {
  documents: Document
}

function InneihRecordRow({
  inneihRecord,
  documentId,
}: {
  documentId: string
  inneihRecord: NonNullable<GetInneihRecordListQuery['getInneihRecordList']['data']>[number]
}) {
  const record = inneihRecord
  const navigate = useNavigate()

  return (
    <TableRow
      role="link"
      onClick={() => {
        navigate(`/category/${INNEIH_CATEGORY_ID}/view/${documentId}`)
      }}
      className="hover:bg-muted"
    >
      <TableCell>{record.inneih_registration_no || '-'}</TableCell>
      <TableCell>
        {record.mipa_hming}
        ,
        {' '}
        {record.hmeichhe_hming}
      </TableCell>
      <TableCell>{record.mipa_pa_hming}</TableCell>
      <TableCell>{record.hmeichhe_pa_hming}</TableCell>
      <TableCell>{record.inneihtirtu}</TableCell>
      <TableCell>{record.hmun}</TableCell>
      <TableCell>{record.inneih_ni ? format(new Date(record.inneih_ni), 'dd-MMM-yyyy') : '-'}</TableCell>
    </TableRow>
  )
}

export default function InneihTable({ documents }: Props) {
  return (
    <div className="my-4 flex grow rounded-md bg-white p-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-32">Reg no</TableHead>
            <TableHead className="w-32">Inneihte Nupa Hming</TableHead>
            <TableHead className="w-32">Mipa Pa Hming</TableHead>
            <TableHead className="w-32">Hmeichhe Pa Hming</TableHead>
            <TableHead className="w-32">Inneihtirtu Hming</TableHead>
            <TableHead className="w-32">Veng Hming</TableHead>
            <TableHead className="w-32">Inneih Ni</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents && documents.length > 0
            ? (
                documents.map(document => (
                  <InneihRecordRow
                    documentId={document.document.id}
                    key={document.id}
                    inneihRecord={document}
                  />
                ))
              )
            : (
                <TableRow>
                  <TableCell className="text-center text-muted-foreground" colSpan={7}>No records found</TableCell>
                </TableRow>
              )}
        </TableBody>
      </Table>
    </div>
  )
}
