import { useQuery } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_DOCUMENT_BY_ID } from './graphql'

export default function useGetDocumentById(id: string) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-document-by-id', id],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENT_BY_ID,
        variables: {
          id,
        },
      })
    },
  })

  const categoryName = data?.getDocumentById?.category?.name
  const parentName = data?.getDocumentById?.category?.parent?.id !== '1' ? data?.getDocumentById?.category?.parent?.name : null
  const parentId = data?.getDocumentById?.category?.parent?.id
  const grandParentName = data?.getDocumentById?.category?.parent?.parent?.id !== '1' ? data?.getDocumentById?.category?.parent?.parent?.name : null
  const grandParentId = data?.getDocumentById?.category?.parent?.parent?.id

  return { data, isLoading, isError, categoryName, parentName, grandParentName, parentId, grandParentId }
}
