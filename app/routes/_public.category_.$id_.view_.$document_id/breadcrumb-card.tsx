import { Link } from 'react-router'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '~/components/ui/breadcrumb'
import { Card, CardContent } from '~/components/ui/card'
import useGetDocumentById from './use-get-document-by-id'

interface Props {
  id: string
}

export default function BreadcrumbCard({ id }: Props) {
  const { data, categoryName, parentName, grandParentName, parentId, grandParentId } = useGetDocumentById(id)

  return (
    <Card className="my-4 bg-white">
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <Breadcrumb className="pt-4 pb-4">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator>
                  /
                </BreadcrumbSeparator>

                {grandParentName && (
                  <>
                    <BreadcrumbItem>
                      <BreadcrumbLink href={`/?main_category_id=${grandParentId}`}>
                        {grandParentName}
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator>
                      /
                    </BreadcrumbSeparator>
                  </>
                )}
                {parentName && (
                  <>
                    <BreadcrumbItem>
                      {!grandParentName
                        ? (
                            <>
                              <BreadcrumbLink href={`/?main_category_id=${parentId}`}>
                                {parentName}
                              </BreadcrumbLink>
                            </>
                          )
                        : (
                            <>
                              <BreadcrumbLink href={`/?main_category_id=${grandParentId}&sub_category_id=${parentId}`}>
                                {parentName}
                              </BreadcrumbLink>
                            </>
                          )}
                    </BreadcrumbItem>
                    <BreadcrumbSeparator>
                      /
                    </BreadcrumbSeparator>
                  </>
                )}
                <BreadcrumbItem>
                  <BreadcrumbLink href={`/category/${data?.getDocumentById?.category?.id}`}>
                    {categoryName}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator>
                  /
                </BreadcrumbSeparator>
                <BreadcrumbItem>
                  {data?.getDocumentById?.title && data?.getDocumentById?.title !== 'null' ? data?.getDocumentById?.title : 'Details'}
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <div>
            <Link to="/">
              <img src="/pci_logo.png" className="size-16" alt="PCI Logo" />
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
