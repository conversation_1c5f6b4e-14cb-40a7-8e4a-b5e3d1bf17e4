# SPA Build Deployment with Git Repository
# NOTE: Using solution 1

This document explains how to maintain a separate git repository for your SPA build files while preventing the build process from removing the git configuration.

## Problem

When building the SPA, React Router cleans the entire `build/client` directory, which removes any git repository that was initialized there.

## Solutions

### Solution 1: Build with Git Preservation (Recommended)

Use the `build-with-git.sh` script that backs up and restores the `.git` directory during builds.

#### Usage:
```bash
# First time setup
npm run build:production  # Regular build
./scripts/init-build-git.sh  # Initialize git in build directory

# For subsequent builds
npm run build:spa  # Uses the git-preserving script
```

#### How it works:
1. Backs up the `.git` directory before build
2. Runs the normal React Router build
3. Restores the `.git` directory after build
4. Shows git status

### Solution 2: Separate Deploy Directory

Use the `build-with-symlink.sh` script that builds to a separate directory outside your main project.

#### Usage:
```bash
./scripts/build-with-symlink.sh
```

#### How it works:
1. Creates a separate deploy directory (`../spa-deploy`)
2. Initializes git repository there
3. Copies build files while preserving git history
4. Automatically commits changes

## Setup Instructions

### For Solution 1 (Git Preservation):

1. **Initial Setup:**
   ```bash
   # Build your project first
   npm run build:production
   
   # Initialize git repository in build directory
   ./scripts/init-build-git.sh
   
   # Add your remote repository
   cd build/client
   git remote add origin <your-spa-repo-url>
   git push -u origin main
   ```

2. **Daily Workflow:**
   ```bash
   # Use the git-preserving build script
   npm run build:spa
   
   # Navigate to build directory and push changes
   cd build/client
   git add .
   git commit -m "Build update: $(date)"
   git push
   ```

### For Solution 2 (Separate Directory):

1. **Initial Setup:**
   ```bash
   # Run the script (it will initialize everything)
   ./scripts/build-with-symlink.sh
   
   # Add your remote repository
   cd ../spa-deploy
   git remote add origin <your-spa-repo-url>
   git push -u origin main
   ```

2. **Daily Workflow:**
   ```bash
   # The script handles everything including git commits
   ./scripts/build-with-symlink.sh
   
   # Push to remote
   cd ../spa-deploy
   git push
   ```

## Package.json Scripts

The following script has been added to your `package.json`:

- `npm run build:spa` - Builds the SPA while preserving git repository

## Files Created

- `scripts/build-with-git.sh` - Main build script with git preservation
- `scripts/init-build-git.sh` - Helper to initialize git in build directory
- `scripts/build-with-symlink.sh` - Alternative approach using separate directory
- `BUILD_DEPLOYMENT.md` - This documentation

## Recommendations

- **Use Solution 1** if you want the git repository directly in your build directory
- **Use Solution 2** if you prefer to keep deployment files completely separate
- Always test the scripts in a development environment first
- Consider adding the deploy directory to your main project's `.gitignore` if using Solution 2

## Troubleshooting

- If scripts fail, ensure they have execute permissions: `chmod +x scripts/*.sh`
- If git commands fail, ensure git is properly configured with user name and email
- For Windows users, consider using Git Bash or WSL to run these scripts
