#!/bin/bash

# Build script that preserves git repository in build/client directory

BUILD_DIR="build/client"
TEMP_GIT_DIR="/tmp/build-client-git-backup"

echo "🔧 Building SPA with git preservation..."

# Step 1: Backup .git directory if it exists
if [ -d "$BUILD_DIR/.git" ]; then
    echo "📦 Backing up git repository..."
    rm -rf "$TEMP_GIT_DIR"
    cp -r "$BUILD_DIR/.git" "$TEMP_GIT_DIR"
    echo "✅ Git repository backed up"
else
    echo "ℹ️  No existing git repository found in $BUILD_DIR"
fi

# Step 2: Run the build
echo "🏗️  Running React Router build..."
pnpm run build:production

# Step 3: Restore .git directory
if [ -d "$TEMP_GIT_DIR" ]; then
    echo "🔄 Restoring git repository..."
    cp -r "$TEMP_GIT_DIR" "$BUILD_DIR/.git"
    rm -rf "$TEMP_GIT_DIR"
    echo "✅ Git repository restored"
fi

echo "🎉 Build complete with git repository preserved!"

# Optional: Show git status
if [ -d "$BUILD_DIR/.git" ]; then
    echo ""
    echo "📊 Git status in build directory:"
    cd "$BUILD_DIR"
    git status --short
fi
